serverless.yaml:
lines : 46, 

providers_CRUDPlatform_RDS_S3.py:
lines: 60, 105, 

validations.py:
lines: 171, 




Objective:
generate code for new entity platform_configs to providers_CRUDPlatform_RDS_S3.py and handle it , the create new validator for this entity in validations.py

Entity: platform_configs

Key Fields and Data Types:

id: Primary Key (PK), integer, auto-incrementing.

platform_id: Foreign Key (FK) referencing the id field of the platforms table. Integer, NOT NULL.

platform_credentials_id: Foreign Key (FK) referencing the id field of the platform_credentials table. Integer, NULLABLE.

name: Varchar, NOT NULL.

resource: Varchar, NOT NULL.

filter: Varchar, NOT NULL.

expand: Varchar, NULLABLE.

top: Integer, NOT NULL.

skip: Integer, NULLABLE.

orderby: Varchar, NULLABLE.

select: Varchar, NULLABLE.

pretty_enums: Boolean, NULLABLE.

options: JSON, NULLABLE.

creation_date: Datetime, NOT NULL (should be set automatically on creation).

modification_date: Datetime, NOT NULL (should be set automatically on creation and updated on modification).

Backend Implementation Requirements:

Update providers_CRUDPlatform_RDS_S3.py:

Add a new entry for platform_configs within the entity_lookup_data dictionary.

Specify the TABLE name as platform_configs.

Define ACCEPTABLE_INPUT_FIELDS based on the fields listed above (excluding id, creation_date, modification_date as these are typically managed by the backend).

Specify LIST_FILTER_ACCEPTABLE_FIELDS_KEY including relevant fields for filtering (id, name, resource).

Set ACCEPTABLE_SELECTED_FIELDS to *.

Assign an appropriate INPUT_VALIDATOR_FUNCTION (this will be created in validations.py).

Determine an appropriate PARENT_ENTITY_FOR_PERMISSIONS if applicable, or define new permission logic.

Update validations.py:

Create a new validation function (e.g., platform_configs_post_put_input_data_validator) for the platform_configs entity.

This function should validate each field based on its data type (e.g., integer, string, boolean, JSON), nullability, and any specific constraints (e.g., string length, choice values if they were specified, though not explicitly in this image for platform_configs other than implied types).

Ensure that platform_id is a valid integer.

Ensure that if platform_credentials_id is provided, it's a valid integer.

Validate options as a JSON object if provided.

The validator should handle both POST (creation) and PUT (update) scenarios, considering required fields for POST.

Lambda Handler Logic (providers_CRUDPlatform_RDS_S3.py):

Ensure the existing lambda_handler can correctly route requests for the platform_configs entity to the appropriate CRUD (GET, POST, PUT, DELETE) handlers.

Frontend Interaction Considerations (for Backend Support):

Dropdown for platform_id: The frontend will need to display a dropdown list where users can select a platform by its name (from the platforms table), but the backend should store the corresponding platform_id.

Consider if a new API endpoint or an enhancement to an existing GET endpoint for platforms is needed to efficiently fetch a list of platforms with their id and name (e.g., GET /platforms?fields=id,name).

Dropdown for platform_credentials_id: Similarly, the frontend will display a dropdown for selecting platform credentials by name (from the platform_credentials table), storing the platform_credentials_id.

Consider if a new API endpoint or an enhancement is needed to fetch platform_credentials with their id and name (e.g., GET /platform_credentials?fields=id,name).

This dropdown should also support a "None" or "Empty" option as platform_credentials_id is nullable.

General Guidelines:

Adhere to the existing coding style, error handling, logging, and response structure.

Ensure database transactions are handled correctly.

Generate comprehensive comments and docstrings.

The solution should be robust and secure.

Deliverables:

Modified providers_CRUDPlatform_RDS_S3.py file.

Modified validations.py file.

SQL script for creating the platform_configs table based on the diagram.