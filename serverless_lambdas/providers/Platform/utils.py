"""
Platform Service Utility Functions

This module provides utility functions and constants for the Platform service Lambda functions,
primarily focused on S3 file operations. It includes:

1. Constants and Configuration:
   - Environment variables for CloudFront URL and S3 bucket
   - Entity type definitions (MLS, PLATFORM, PLATFORM_CREDENTIAL)
   - Dictionary keys for entity lookup data structure
   - Error code prefixes and specific error codes

2. S3 File Operations:
   - change_s3(): Moves a file to a new location in S3 and returns CloudFront URL
   - move_s3_file(): Core function for copying files between S3 locations
   - delete_file_from_s3(): Removes files from S3 bucket

3. Exception Handling:
   - S3ActionException: Custom exception for S3 operation failures

These utilities support the Platform CRUD operations by managing file storage
for platform images and other assets, with proper error handling and logging.
"""

import logging
import os
import boto3  # AWS SDK for Python, used to interact with AWS services like S3


logger = logging.getLogger()  # Creates a logger instance for this module to track operations and errors


CLOUDFRONT_URL = os.environ.get("CLOUDFRONT_URL")  # Retrieves CloudFront distribution URL from environment variables for serving S3 files
S3_BUCKET = os.environ.get("S3_BUCKET")  # Gets S3 bucket name from environment variables where files are stored

# entities
MLS = "mls_server"  # Constant defining the MLS entity type for permissions and path construction
PLATFORM = "platform"  # Constant defining the platform entity type for CRUD operations
PLATFORM_CREDENTIAL = "platform_credential"  # Constant defining the platform credential entity type for authentication data

# lookup data dict keys
TABLE = "table_key"  # Key for database table name in entity configuration
INPUT_VALIDATOR_FUNCTION = "input_validator_function_holder_key"  # Key for validation function reference
ACCEPTABLE_INPUT_FIELDS = "acceptable_input_fields_key"  # Key for allowed input fields set
ACCEPTABLE_SELECTED_FIELDS = "acceptable_selected_fields_key"  # Key for fields that can be returned in queries
LIST_FILTER_ACCEPTABLE_FIELDS_KEY = "list_filter_acceptable_fields_key"  # Key for fields that can be used in filter conditions
PARENT_ENTITY_FOR_PERMISSIONS = "parent_entity_for_permissions"  # Key for parent entity used in permission inheritance

# Error prefix code
ERROR_CODE_PREFIX = "prv-05-02"  # Prefix for all error codes in this module for consistent error tracking
NOT_FOUND_RECORD_ERROR_CODE = f"{ERROR_CODE_PREFIX}-001"  # Specific error code for record not found condition


class S3ActionException(Exception):
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)  # Custom exception for S3 operation failures, inherits from base Exception


def change_s3(new_path, file_path, prefix="s3://realtyfeed/"):
    """
    Change file location in file_path to new_path location.
    """
    logger.info(
        f"Start change_s3(new_path={new_path}, file_path={file_path}, prefix={prefix})..."  # Logs function start with parameters for tracing
    )
    current_path = file_path.replace(prefix, "")  # Removes S3 URI prefix to get relative path
    new_path = f"{new_path}/{current_path.split('/')[-1]}"  # Constructs new path preserving original filename

    if move_s3_file(source_path=current_path, destination_path=new_path):  # Calls lower-level function to perform actual file move
        function_result = f"{CLOUDFRONT_URL}/{new_path}"  # Constructs public-facing CloudFront URL for the moved file

    else:
        logger.error(f"ERROR | change_s3() |")  # Logs error if file move operation failed
        raise S3ActionException()  # Raises custom exception to be caught by caller

    return function_result  # Returns CloudFront URL to the new file location


def move_s3_file(
    source_path,
    destination_path,
    source_prefix="s3://realtyfeed/",
    destination_prefix="s3://realtyfeed/",
):
    logger.info(
        f"Start move_s3_file( source_path={source_path}, destination_path={destination_path}, source_prefix={source_prefix}, destination_prefix={destination_prefix})..."  # Logs function start with parameters
    )
    if not source_path or not destination_path:  # Validates required parameters
        return  # Returns early if either path is missing

    try:
        source_path = source_path.replace(source_prefix, "")  # Removes S3 URI prefix from source path
        destination_path = destination_path.replace(destination_prefix, "")  # Removes S3 URI prefix from destination path

        s3_resource = boto3.client("s3", region_name="us-east-1")  # Creates S3 client with specific region
        object_metadata = s3_resource.list_objects_v2(
            Bucket=S3_BUCKET, Prefix=source_path  # Checks if source object exists and gets its metadata
        )
        new_object_metadata = s3_resource.list_objects_v2(
            Bucket=S3_BUCKET, Prefix=destination_path  # Checks if destination already has an object
        )

        if object_metadata.get("Contents"):  # Proceeds only if source object exists
            # Check distination if is not empty clear destination.
            if new_object_metadata.get("Contents"):  # If destination already has an object
                s3_resource.delete_object(
                    Bucket=S3_BUCKET,
                    Key=new_object_metadata["Contents"][0]["Key"],  # Deletes existing object at destination to avoid conflicts
                )

            copy_source = {
                "Bucket": S3_BUCKET,
                "Key": source_path,  # Specifies source object for copy operation
            }
            s3_resource.copy(copy_source, S3_BUCKET, destination_path)  # Copies object to new location
            s3_resource.delete_object(
                Bucket=S3_BUCKET, Key=object_metadata["Contents"][0]["Key"]  # Deletes original object after successful copy
            )

        else:
            return False  # Returns failure if source object doesn't exist

        return True  # Returns success if all operations completed

    except Exception as e:
        logger.exception(f"ERROR={e}")  # Logs detailed exception information including stack trace
    return False  # Returns failure for any exceptions during S3 operations


def delete_file_from_s3(file_path, prefix="s3://realtyfeed/"):
    logger.info(
        f"Start delete_file_from_s3((file_path={file_path}, prefix={prefix})..."  # Logs function start with parameters
    )
    if not file_path:  # Validates required parameter
        return  # Returns early if path is missing

    try:
        s3_resource = boto3.client("s3", region_name="us-east-1")  # Creates S3 client with specific region
        path = file_path.replace(prefix, "")  # Removes S3 URI prefix to get relative path

        object_metadata = s3_resource.list_objects_v2(Bucket=S3_BUCKET, Prefix=path)  # Checks if objects exist with this prefix
        if object_metadata.get("Contents"):  # Proceeds only if matching objects exist
            for content in object_metadata["Contents"]:  # Iterates through all matching objects
                s3_resource.delete_object(Bucket=S3_BUCKET, Key=content["Key"])  # Deletes each matching object

    except Exception as e:
        logger.exception(f"ERROR={e}")  # Logs detailed exception information including stack trace
        raise S3ActionException()  # Raises custom exception to be caught by caller
