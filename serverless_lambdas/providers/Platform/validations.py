"""
Platform Service Validation Module

This module provides data validation functions for the Platform service Lambda functions.
It ensures data integrity and proper formatting before database operations. Key components:

1. Constants and Configuration:
   - Dictionary keys for entity lookup data structure
   - Error code prefix for consistent error reporting

2. Entity-Specific Validators:
   - platform_post_put_input_data_validator(): Validates platform entity fields
     including name, image, dashboard_access_url, username, password, and extra_data
   - platform_credential_post_put_input_data_validator(): Validates credential fields
     including name, platform_id, feed_type_connection_type_id, request_url, client_id,
     client_password, scope, generate_token, and options

3. Generic Validation Function:
   - validate_data(): Orchestrates validation process based on entity type and HTTP method
     - Validates entity existence and required fields
     - <PERSON>les specific validation for GET, POST, PUT, DELETE operations
     - Validates list filters and query parameters
     - Delegates to entity-specific validators for field-level validation

Each validator returns a list of error objects with code and message properties,
which are used by the main Lambda handler to provide detailed error responses.
"""

import logging
import os

from realtyfeed.exceptions import DatabaseException, DataInvalidException
from realtyfeed.orm import RDSHandler


logger = logging.getLogger()


# lookup data dict keys
TABLE = "table_key"  # Key used to identify the database table name in entity configuration
INPUT_VALIDATOR_FUNCTION = "input_validator_function_holder_key"  # Key that stores reference to entity-specific validation function
ACCEPTABLE_INPUT_FIELDS = "acceptable_input_fields_key"  # Key for set of field names allowed in API requests
LIST_FILTER_ACCEPTABLE_FIELDS_KEY = "list_filter_acceptable_fields_key"  # Key for fields that can be used in filter queries

# Error prefix code
ERROR_CODE_PREFIX = "prv-05-02"  # Standardized prefix for all validation error codes in this module


# POST/PUT input data validation functions
# NOTE: all validators change the choice fields to lower case if there is not any problem.
def platform_post_put_input_data_validator(data, http_method=None):
    """
    Validates input data for platform entity during POST or PUT operations.

    This function performs field-level validation for the platform entity:
    - For POST: Ensures required fields are present and properly formatted
    - For PUT: Validates only the fields that are present in the request

    Each validation failure adds a structured error object to the errors list
    with a specific error code and descriptive message.

    Args:
        data (dict): The request data containing platform fields
        http_method (str): The HTTP method (POST or PUT) to determine validation context

    Returns:
        list: List of error objects, each with 'code' and 'message' properties
    """
    errors = []  # Initialize empty list to collect validation errors

    # Extract all fields from the request data for validation
    name = data.get("name")  # Platform name (required for POST, optional for PUT)
    image = data.get("image")  # Platform logo/image URL (optional)
    dashboard_access_url = data.get("dashboard_access_url")  # URL for accessing platform dashboard (required for POST)
    username = data.get("username")  # Username for platform authentication (optional)
    password = data.get("password")  # Password for platform authentication (optional)
    extra_data = data.get("extra_data")  # Additional platform-specific data as JSON (optional)
    is_post = http_method.upper() == "POST"  # Flag to determine if this is a POST request for conditional validation

    # Validate name field - required for POST, optional for PUT but must be valid if present
    # Complex condition broken down:
    # 1. For POST: name must exist AND be a string with length <= 200
    # 2. For PUT: if name is provided, it must be a string with length <= 200
    if (is_post and (not name or not (isinstance(name, str) and len(name) <= 200))) or (
        not is_post and name and not (isinstance(name, str) and len(name) <= 200)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03141",  # Specific error code for name field validation
                "message": f"'{http_method}' request 'name' field error: The 'name' field must be a String with maximum 200 charachters length.",
            }
        )

    # Validate image field - optional but must be a string URL if present
    if image and not isinstance(image, str):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03142",  # Specific error code for image field validation
                "message": f"'{http_method}' request 'image' field error: The 'image' field must be an URL.",
            }
        )

    # Validate dashboard_access_url - required for POST, optional for PUT but must be valid if present
    # Complex condition broken down:
    # 1. For POST: dashboard_access_url must exist AND be a string
    # 2. For PUT: if dashboard_access_url is provided, it must be a string
    if (
        is_post
        and (not dashboard_access_url or not isinstance(dashboard_access_url, str))
    ) or (
        not is_post
        and dashboard_access_url
        and not isinstance(dashboard_access_url, str)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03143",  # Specific error code for dashboard_access_url validation
                "message": f"'{http_method}' request 'dashboard_access_url' field error: The 'dashboard_access_url' field must be an URL.",
            }
        )

    # Validate extra_data field - optional but must be a dictionary (JSON) if present
    if extra_data and not isinstance(extra_data, dict):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03144",  # Specific error code for extra_data field validation
                "message": f"'{http_method}' request 'extra_data' field error: The 'extra_data' field must be JSON.",
            }
        )

    # Validate username field - optional but must be a string with length <= 200 if present
    if username and not (isinstance(username, str) and len(username) <= 200):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03145",  # Specific error code for username field validation
                "message": f"'{http_method}' request 'username' field error: The 'username' field must be a String with maximum 200 charachters length.",
            }
        )

    # Validate password field - optional but must be a string with length <= 256 if present
    if password and not (isinstance(password, str) and len(password) <= 256):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03146",  # Specific error code for password field validation
                "message": f"'{http_method}' request 'password' field error: The 'password' field must be a String with maximum 200 charachters length.",
            }
        )
    return errors  # Return collected validation errors


def platform_credential_post_put_input_data_validator(data, http_method=None):
    """
    Validates input data for platform_credential entity during POST or PUT operations.

    This function performs field-level validation for credential fields:
    - For POST: Ensures required fields (platform_id, feed_type_connection_type_id, request_url) are present and valid
    - For PUT: Validates only the fields that are present in the request

    Each validation failure adds a structured error object to the errors list
    with a specific error code and descriptive message.

    Args:
        data (dict): The request data containing credential fields
        http_method (str): The HTTP method (POST or PUT) to determine validation context

    Returns:
        list: List of error objects, each with 'code' and 'message' properties
    """
    errors = []  # Initialize empty list to collect validation errors

    ##### add grant_type to credentials

    # Extract all fields from the request data for validation
    name = data.get("name")  # Credential name (optional)
    platform_id = data.get("platform_id")  # Foreign key to associated platform (required for POST)
    feed_type_connection_type_id = data.get("feed_type_connection_type_id")  # Type of feed connection (required for POST)
    # token_url = data.get("token_url") This field does not need to validate.
    request_url = data.get("request_url")  # URL for API requests (required for POST)
    client_id = data.get("client_id")  # Client ID for authentication (optional)
    client_password = data.get("client_password")  # Client password/secret for authentication (optional)
    # access_token = data.get("access_token") This field does not need to validate.
    scope = data.get("scope")  # OAuth scope (optional)
    generate_token = data.get("generate_token")  # Flag to auto-generate tokens (optional)
    options = data.get("options")  # Additional credential-specific options as JSON (optional)

    is_post = http_method.upper() == "POST"  # Flag to determine if this is a POST request for conditional validation

    # Validate name field - optional but must be a string with length <= 200 if present
    if name and not (isinstance(name, str) and len(name) <= 200):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03151",  # Specific error code for name field validation
                "message": f"'{http_method}' request 'name' field error: The 'name' field must be String.",
            }
        )

    # Validate platform_id - required for POST, optional for PUT but must be an integer if present
    # Complex condition broken down:
    # 1. For POST: platform_id must exist AND be an integer
    # 2. For PUT: if platform_id is provided, it must be an integer
    if (is_post and (not platform_id or not isinstance(platform_id, int))) or (
        not is_post and platform_id and not isinstance(platform_id, int)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03152",  # Specific error code for platform_id field validation
                "message": f"'{http_method}' request 'platform_id' field error: The request data must have an Integer 'platform_id' field.",
            }
        )

    # Validate feed_type_connection_type_id - required for POST, optional for PUT but must be an integer if present
    # Complex condition broken down:
    # 1. For POST: feed_type_connection_type_id must exist AND be an integer
    # 2. For PUT: if feed_type_connection_type_id is provided, it must be an integer
    if (
        is_post
        and (
            not feed_type_connection_type_id
            or not isinstance(feed_type_connection_type_id, int)
        )
    ) or (
        not is_post
        and feed_type_connection_type_id
        and not isinstance(feed_type_connection_type_id, int)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03153",  # Specific error code for feed_type_connection_type_id validation
                "message": f"'{http_method}' request 'feed_type_connection_type_id' field error: The request data must have an Integer 'platform_id' field.",
            }
        )

    # Validate request_url - required for POST only
    if is_post and not request_url:
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03154",  # Specific error code for request_url field validation
                "message": f"'{http_method}' request 'request_url' field error: The request data must have an URL string 'request_url' field.",
            }
        )

    # Validate client_id field - optional but must be a string with length <= 200 if present
    if client_id and not (isinstance(client_id, str) and len(client_id) <= 200):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03155",  # Specific error code for client_id field validation
                "message": f"'{http_method}' request 'client_id' field error: The 'client_id' field must be String.",
            }
        )

    # Validate client_password field - optional but must be a string with length <= 256 if present
    if client_password and not (
        isinstance(client_password, str) and len(client_password) <= 256
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03156",  # Specific error code for client_password field validation
                "message": f"'{http_method}' request 'client_password' field error: The 'client_password' field must be String.",
            }
        )

    # Validate scope field - optional but must be a string with length <= 200 if present
    if scope and not (isinstance(scope, str) and len(scope) <= 200):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03157",  # Specific error code for scope field validation
                "message": f"'{http_method}' request 'scope' field error: The 'scope' field must be String.",
            }
        )

    # Validate generate_token field - optional but must be a boolean if present
    if generate_token and not isinstance(generate_token, bool):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03158",  # Specific error code for generate_token field validation
                "message": f"'{http_method}' request 'generate_token' field error: The 'generate_token' field must be Boolean.",
            }
        )

    # Validate options field - optional but must be a dictionary (JSON) if present
    if options and not isinstance(options, dict):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03159",  # Specific error code for options field validation
                "message": f"'{http_method}' request 'options' field error: The 'options' field must be JSON.",
            }
        )

    return errors  # Return collected validation errors


def platform_configs_post_put_input_data_validator(data, http_method=None):
    """
    Validates input data for platform_configs entity during POST or PUT operations.

    This function performs field-level validation for platform configuration fields:
    - For POST: Ensures required fields (platform_id, name, resource, filter, top) are present and valid
    - For PUT: Validates only the fields that are present in the request

    Each validation failure adds a structured error object to the errors list
    with a specific error code and descriptive message.

    Args:
        data (dict): The request data containing platform config fields
        http_method (str): The HTTP method (POST or PUT) to determine validation context

    Returns:
        list: List of error objects, each with 'code' and 'message' properties
    """
    errors = []  # Initialize empty list to collect validation errors

    # Extract all fields from the request data for validation
    platform_id = data.get("platform_id")  # Foreign key to associated platform (required for POST)
    platform_credentials_id = data.get("platform_credentials_id")  # Foreign key to platform credentials (optional)
    name = data.get("name")  # Configuration name (required for POST)
    resource = data.get("resource")  # Resource identifier (required for POST)
    filter_param = data.get("filter")  # Filter parameter (required for POST)
    expand = data.get("expand")  # Expand parameter (optional)
    top = data.get("top")  # Top parameter (required for POST)
    skip = data.get("skip")  # Skip parameter (optional)
    orderby = data.get("orderby")  # Order by parameter (optional)
    select = data.get("select")  # Select parameter (optional)
    pretty_enums = data.get("pretty_enums")  # Pretty enums flag (optional)
    options = data.get("options")  # Additional options as JSON (optional)

    is_post = http_method.upper() == "POST"  # Flag to determine if this is a POST request for conditional validation

    # Validate platform_id - required for POST, optional for PUT but must be an integer if present
    if (is_post and (not platform_id or not isinstance(platform_id, int))) or (
        not is_post and platform_id and not isinstance(platform_id, int)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03161",  # Specific error code for platform_id field validation
                "message": f"'{http_method}' request 'platform_id' field error: The request data must have an Integer 'platform_id' field.",
            }
        )

    # Validate platform_credentials_id - optional but must be an integer if present
    if platform_credentials_id and not isinstance(platform_credentials_id, int):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03162",  # Specific error code for platform_credentials_id field validation
                "message": f"'{http_method}' request 'platform_credentials_id' field error: The 'platform_credentials_id' field must be an Integer.",
            }
        )

    # Validate name field - required for POST, optional for PUT but must be valid if present
    if (is_post and (not name or not (isinstance(name, str) and len(name) <= 200))) or (
        not is_post and name and not (isinstance(name, str) and len(name) <= 200)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03163",  # Specific error code for name field validation
                "message": f"'{http_method}' request 'name' field error: The 'name' field must be a String with maximum 200 characters length.",
            }
        )

    # Validate resource field - required for POST, optional for PUT but must be valid if present
    if (is_post and (not resource or not (isinstance(resource, str) and len(resource) <= 200))) or (
        not is_post and resource and not (isinstance(resource, str) and len(resource) <= 200)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03164",  # Specific error code for resource field validation
                "message": f"'{http_method}' request 'resource' field error: The 'resource' field must be a String with maximum 200 characters length.",
            }
        )

    # Validate filter field - required for POST, optional for PUT but must be valid if present
    if (is_post and (not filter_param or not (isinstance(filter_param, str) and len(filter_param) <= 500))) or (
        not is_post and filter_param and not (isinstance(filter_param, str) and len(filter_param) <= 500)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03165",  # Specific error code for filter field validation
                "message": f"'{http_method}' request 'filter' field error: The 'filter' field must be a String with maximum 500 characters length.",
            }
        )

    # Validate expand field - optional but must be a string if present
    if expand and not (isinstance(expand, str) and len(expand) <= 200):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03166",  # Specific error code for expand field validation
                "message": f"'{http_method}' request 'expand' field error: The 'expand' field must be a String with maximum 200 characters length.",
            }
        )

    # Validate top field - required for POST, optional for PUT but must be an integer if present
    if (is_post and (not top or not isinstance(top, int) or top <= 0)) or (
        not is_post and top and (not isinstance(top, int) or top <= 0)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03167",  # Specific error code for top field validation
                "message": f"'{http_method}' request 'top' field error: The 'top' field must be a positive Integer.",
            }
        )

    # Validate skip field - optional but must be a non-negative integer if present
    if skip and (not isinstance(skip, int) or skip < 0):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03168",  # Specific error code for skip field validation
                "message": f"'{http_method}' request 'skip' field error: The 'skip' field must be a non-negative Integer.",
            }
        )

    # Validate orderby field - optional but must be a string if present
    if orderby and not (isinstance(orderby, str) and len(orderby) <= 200):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03169",  # Specific error code for orderby field validation
                "message": f"'{http_method}' request 'orderby' field error: The 'orderby' field must be a String with maximum 200 characters length.",
            }
        )

    # Validate select field - optional but must be a string if present
    if select and not (isinstance(select, str) and len(select) <= 500):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03170",  # Specific error code for select field validation
                "message": f"'{http_method}' request 'select' field error: The 'select' field must be a String with maximum 500 characters length.",
            }
        )

    # Validate pretty_enums field - optional but must be a boolean if present
    if pretty_enums is not None and not isinstance(pretty_enums, bool):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03171",  # Specific error code for pretty_enums field validation
                "message": f"'{http_method}' request 'pretty_enums' field error: The 'pretty_enums' field must be a Boolean.",
            }
        )

    # Validate options field - optional but must be a dictionary (JSON) if present
    if options and not isinstance(options, dict):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03172",  # Specific error code for options field validation
                "message": f"'{http_method}' request 'options' field error: The 'options' field must be JSON.",
            }
        )

    return errors  # Return collected validation errors


def validate_data(entity_lookup_data, entity, data, http_method):
    """
    This function validate all input data.
    If the http method was POST or PUT the function use entities validators to
    validate input data fields, the function get entity validators from entity_lookup_data.
    """
    # Log the start of validation with input parameters for debugging and tracing
    logger.info(
        f"Start validate_data(entity={entity}, data={data}, http_method={http_method})..."
    )
    errors = []  # Initialize empty list to collect all validation errors

    # VALIDATION STEP 1: Verify entity exists in the lookup data
    # This ensures the requested entity type is valid before proceeding with other validations
    # Error code 0301 indicates an invalid entity type was requested
    if not entity or entity.lower() not in entity_lookup_data.keys():
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-0301",  # Error code for invalid entity
                "message": f"Wrong 'entity': The request body 'entity' field must contain one of {entity_lookup_data.keys()}",
            }
        )

    # VALIDATION STEP 2: Verify data object exists
    # All operations require a data object, even if just containing an ID
    # Error code 0302 indicates missing data object in request
    elif not data:
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-0302",
                "message": "Missing 'data' field error: The request must have 'data' field.",
            }
        )

    # VALIDATION STEP 3: HTTP Method-specific validation
    # DELETE validation - only requires a valid integer ID
    elif http_method == "DELETE":
        id = data.get("id")  # Extract ID from data for DELETE operation
        # For DELETE, only validate that ID exists and is an integer
        # Error code 0303 indicates missing or invalid ID for DELETE operation
        if not id or not isinstance(id, int):
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-0303",
                    "message": "'DELETE' request error: The request data must have an integer 'id' field.",
                }
            )

    # GET validation - handles both single record retrieval (by ID) and filtered list retrieval
    elif http_method == "GET":
        # Extract key parameters from the GET request data
        id = data.get("id")  # ID for single record retrieval
        list_filter = data.get("list_filter")  # Filter parameters for list retrieval

        # Commented code block for future feature - validating selected fields
        # This would restrict which fields can be returned in the response
        # TODO: For future featurs. We can use this validator in future...
        # acceptable_selected_fields = set(
        #     entity_lookup_data[entity][ACCEPTABLE_SELECTED_FIELDS]
        # )
        # if acceptable_selected_fields != {"*"}:
        #     selected_fields = data.get("selected_fields")
        #     if selected_fields and not set(selected_fields).issubset(
        #         acceptable_selected_fields
        #     ):
        #         errors.append(
        #             {
        #                 "code": f"{ERROR_CODE_PREFIX}-0304",
        #                 "message": f"'GET' unacceptable 'selected_fields' error: Acceptable selected fields are {acceptable_selected_fields}. the request contain {set(selected_fields)-acceptable_selected_fields} fields that are not in acceptable selected fields",
        #             }
        #         )

        # Validate ID if provided - must be an integer
        # Error code 0305 indicates invalid ID format for GET operation
        if id and not isinstance(id, int):
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-0305",
                    "message": "'GET' request 'id' error: The request data must have an integer 'id' field.",
                }
            )

        # Validate list_filter if provided - must be a dictionary with valid parameters
        elif list_filter and isinstance(list_filter, dict):
            # Extract filter components for validation
            filters = list_filter.get("filters")  # Filter conditions
            order_by = list_filter.get("order_by")  # Sorting field and direction
            limit = list_filter.get("limit")  # Maximum number of records to return
            offset = list_filter.get("offset")  # Number of records to skip

            # Validate order_by - must be a string if provided
            # Error code 0306 indicates invalid order_by format
            if order_by and not isinstance(order_by, str):
                errors.append(
                    {
                        "code": f"{ERROR_CODE_PREFIX}-0306",
                        "message": "'GET' request 'list_filte'/'oreder_by' error: The 'order_by' field of 'list_filter' data must be an String.",
                    }
                )

            # Validate limit - must be an integer if provided
            # Error code 0307 indicates invalid limit format
            if limit and not isinstance(limit, int):
                errors.append(
                    {
                        "code": f"{ERROR_CODE_PREFIX}-0307",
                        "message": "'GET' request 'list_filte'/'limit' error: The 'limit' field of 'list_filter' data must be an Integer.",
                    }
                )

            # Validate offset - must be an integer if provided
            # Error code 0308 indicates invalid offset format
            if offset and not isinstance(offset, int):
                errors.append(
                    {
                        "code": f"{ERROR_CODE_PREFIX}-0308",
                        "message": "'GET' request 'list_filte'/'offset' error: The 'offset' field of 'list_filter' data must be an Integer.",
                    }
                )

            # Validate filters - must be a list if provided
            # Error code 0309 indicates invalid filters format
            if filters and not isinstance(filters, list):
                errors.append(
                    {
                        "code": f"{ERROR_CODE_PREFIX}-0309",
                        "message": "'GET' request 'list_filte'/'filters' error: The 'filters' field of 'list_filter' data must be one of list, json or dict.",
                    }
                )

            # If filters is a list, validate that all filter fields are acceptable for this entity
            elif filters and isinstance(filters, list):
                # Create RDS handler to use its filter validation method
                # This connects to the database to validate filter fields
                try:
                    rds_handler = RDSHandler(
                        table_name=entity_lookup_data[entity][TABLE],  # Get table name from entity config
                        is_cursor_dict=True,  # Return results as dictionaries
                    )
                except DatabaseException as e:
                    # Log database connection errors but continue validation
                    logger.exception(f"ERROR | validate_data() e={e}")

                try:
                    # Use RDS handler to validate filter fields against acceptable fields list
                    # This method will raise DataInvalidException if any field is not acceptable
                    acceptable_list_filter_fields = entity_lookup_data[entity][
                        LIST_FILTER_ACCEPTABLE_FIELDS_KEY  # Get acceptable filter fields from entity config
                    ]
                    list_filter = data.get("list_filter")
                    filters = list_filter.get("filters", "")
                    # This call validates filters against acceptable fields
                    sql_condition = rds_handler.generate_sql_condition_by_filter(
                        conditions=filters,
                        acceptable_fields=acceptable_list_filter_fields,
                    )

                except DataInvalidException as e:
                    # Error code 03091 indicates filter contains fields not in the acceptable list
                    errors.append(
                        {
                            "code": f"{ERROR_CODE_PREFIX}-03091",
                            "message": f"Acceptable fields for using in filters are {entity_lookup_data[entity][LIST_FILTER_ACCEPTABLE_FIELDS_KEY]} fields.",
                        }
                    )

        # Validate list_filter type - must be a dictionary if provided
        # Error code 0310 indicates invalid list_filter format
        elif list_filter and not isinstance(list_filter, dict):
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-0310",
                    "message": "'GET' request wrong 'list_filte' error: The 'list_filter' field must be an JSON.",
                }
            )

        # Validate that either id or list_filter is provided for GET requests
        # Error code 0311 indicates neither id nor list_filter was provided
        elif not id and not list_filter:
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-0311",
                    "message": "'GET' empty body: The 'GET' request data must have either an Integer 'id' field or a JSON 'list_filter' field.",
                }
            )

    # POST/PUT validation - most complex validation with entity-specific rules
    elif http_method in ["PUT", "POST"]:
        # Get the set of acceptable input fields for this entity from the lookup data
        acceptable_input_fields = set(
            entity_lookup_data[entity][ACCEPTABLE_INPUT_FIELDS]  # Fields allowed in requests for this entity
        )
        input_fields = set(data.keys())  # Actual fields provided in the request

        # For PUT requests, validate ID exists and is an integer
        # Also add "id" to acceptable fields since it's required for PUT but not for POST
        if http_method == "PUT":
            id = data.get("id")
            # Error code 0312 indicates missing or invalid ID for PUT operation
            if not id or not isinstance(id, int):
                errors.append(
                    {
                        "code": f"{ERROR_CODE_PREFIX}-0312",
                        "message": "'PUT' request 'id' field error: The request data must have an integer 'id' field.",
                    }
                )
            # Adding id to entity acceptable fields for PUT http method.
            # id is not acceptable in POST http methods.
            acceptable_input_fields.add("id")

        # Validate that all input fields are in the acceptable fields list
        # Error code 0313 indicates request contains fields not allowed for this entity
        if not input_fields.issubset(acceptable_input_fields):
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-0313",
                    "message": f"'{http_method}' unacceptable input fields: the data input fields must be in {acceptable_input_fields}. the input contain {input_fields-acceptable_input_fields} fields that are not acceptable.",
                }
            )

        # Call the entity-specific validator function from the lookup data
        # This performs detailed validation of each field's format and requirements
        # The validator function is referenced by the INPUT_VALIDATOR_FUNCTION key in entity_lookup_data
        errors = errors + entity_lookup_data[entity].get(INPUT_VALIDATOR_FUNCTION)(
            data=data, http_method=http_method  # Pass data and HTTP method to validator
        )
    # Validate HTTP method - must be one of the supported methods
    # Error code 0316 indicates invalid HTTP method
    else:
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-0316",
                "message": f"Wrong HTTP MethodThe: The HTTP method must be one of POST, GET, PUT, and DELETE.",
            }
        )
    return errors  # Return all collected validation errors
