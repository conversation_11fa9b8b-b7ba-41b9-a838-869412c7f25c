-- SQL script for creating the platform_configs table
-- This table stores configuration settings for platform integrations

CREATE TABLE IF NOT EXISTS platform_configs (
    -- Primary key - auto-incrementing integer
    id SERIAL PRIMARY KEY,
    
    -- Foreign key to platforms table - required
    platform_id INTEGER NOT NULL,
    
    -- Foreign key to platform_credentials table - optional
    platform_credentials_id INTEGER,
    
    -- Configuration name - required, max 200 characters
    name VARCHAR(200) NOT NULL,
    
    -- Resource identifier - required, max 200 characters
    resource VARCHAR(200) NOT NULL,
    
    -- Filter parameter - required, max 500 characters
    filter VARCHAR(500) NOT NULL,
    
    -- Expand parameter - optional, max 200 characters
    expand VARCHAR(200),
    
    -- Top parameter for pagination - required, positive integer
    top INTEGER NOT NULL CHECK (top > 0),
    
    -- Skip parameter for pagination - optional, non-negative integer
    skip INTEGER CHECK (skip >= 0),
    
    -- Order by parameter - optional, max 200 characters
    orderby VARCHAR(200),
    
    -- Select parameter - optional, max 500 characters
    select VARCHAR(500),
    
    -- Pretty enums flag - optional boolean
    pretty_enums BOOLEAN,
    
    -- Additional options as JSON - optional
    options JSONB,
    
    -- Audit fields - automatically managed
    creation_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_platform_configs_platform_id 
        FOREIGN KEY (platform_id) 
        REFERENCES platforms(id) 
        ON DELETE CASCADE 
        ON UPDATE CASCADE,
        
    CONSTRAINT fk_platform_configs_platform_credentials_id 
        FOREIGN KEY (platform_credentials_id) 
        REFERENCES platform_credentials(id) 
        ON DELETE SET NULL 
        ON UPDATE CASCADE
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_platform_configs_platform_id ON platform_configs(platform_id);
CREATE INDEX IF NOT EXISTS idx_platform_configs_platform_credentials_id ON platform_configs(platform_credentials_id);
CREATE INDEX IF NOT EXISTS idx_platform_configs_name ON platform_configs(name);
CREATE INDEX IF NOT EXISTS idx_platform_configs_resource ON platform_configs(resource);

-- Create trigger to automatically update modification_date on record updates
CREATE OR REPLACE FUNCTION update_platform_configs_modification_date()
RETURNS TRIGGER AS $$
BEGIN
    NEW.modification_date = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_platform_configs_modification_date
    BEFORE UPDATE ON platform_configs
    FOR EACH ROW
    EXECUTE FUNCTION update_platform_configs_modification_date();

-- Add comments for documentation
COMMENT ON TABLE platform_configs IS 'Stores configuration settings for platform integrations';
COMMENT ON COLUMN platform_configs.id IS 'Primary key - auto-incrementing identifier';
COMMENT ON COLUMN platform_configs.platform_id IS 'Foreign key reference to platforms table';
COMMENT ON COLUMN platform_configs.platform_credentials_id IS 'Optional foreign key reference to platform_credentials table';
COMMENT ON COLUMN platform_configs.name IS 'Configuration name - required';
COMMENT ON COLUMN platform_configs.resource IS 'Resource identifier for the configuration';
COMMENT ON COLUMN platform_configs.filter IS 'Filter parameter for data retrieval';
COMMENT ON COLUMN platform_configs.expand IS 'Optional expand parameter';
COMMENT ON COLUMN platform_configs.top IS 'Top parameter for pagination - must be positive';
COMMENT ON COLUMN platform_configs.skip IS 'Skip parameter for pagination - must be non-negative';
COMMENT ON COLUMN platform_configs.orderby IS 'Optional order by parameter';
COMMENT ON COLUMN platform_configs.select IS 'Optional select parameter';
COMMENT ON COLUMN platform_configs.pretty_enums IS 'Optional flag for pretty enum formatting';
COMMENT ON COLUMN platform_configs.options IS 'Additional configuration options as JSON';
COMMENT ON COLUMN platform_configs.creation_date IS 'Timestamp when record was created';
COMMENT ON COLUMN platform_configs.modification_date IS 'Timestamp when record was last modified';
