# Platform Service Serverless Configuration
#
# This serverless.yaml file defines the AWS Lambda deployment configuration for the Platform microservice:
#
# 1. Service Configuration:
#    - Defines "Platform" as the service name with Serverless Framework v3
#    - Enables environment variables via dotenv
#
# 2. AWS Provider Settings:
#    - Uses Python 3.12 runtime in us-east-1 region
#    - Configures deployment method, versioning, and tracing
#    - Sets up API Gateway integration with existing REST API resources
#    - References environment-specific variables from JSON files
#
# 3. Lambda Function (providers_CRUDPlatform_RDS_S3):
#    - Configures handler, memory (128MB), timeout (5s), and architecture (x86_64)
#    - Sets environment variables for S3, CloudFront, and stage name
#    - Attaches Base layer for shared dependencies
#
# 4. API Gateway Events:
#    - Maps HTTP endpoints for GET/POST/PUT/DELETE operations
#    - Configures request templates to transform API Gateway requests to Lambda format
#    - Sets up response templates for success (200) and error cases (400, 500)
#    - Implements CORS and authorization via external authorizer
#
# 5. IAM Resources:
#    - Creates CustomRole with necessary permissions for Lambda execution
#
# This configuration enables the Platform service to handle CRUD operations for platform
# and platform_credential entities through a RESTful API interface.

service: Platform                # Defines the service name for this Serverless deployment
frameworkVersion: "3"            # Specifies Serverless Framework version compatibility
configValidationMode: warn       # Sets validation mode to warn instead of error on config issues
useDotenv: true                  # Enables loading of environment variables from .env files

provider:
  name: aws                      # Specifies AWS as the cloud provider
  runtime: python3.12            # Sets Python 3.12 as the Lambda runtime environment
  stage: ${opt:stage}            # Uses the stage parameter passed via CLI (dev, staging, prod)
  region: us-east-1              # Deploys resources to AWS us-east-1 region
  profile: default               # Uses the default AWS credentials profile
  stackName: Platform            # Names the CloudFormation stack
  versionFunctions: true         # Enables versioning for Lambda functions
  deploymentMethod: direct       # Uses direct deployment method instead of CloudFormation changeset
  deploymentPrefix: <EMAIL>,<EMAIL>,<EMAIL>  # Sets deployment notification recipients  #### add my email
  deploymentBucket: ${file(../../../environments/${opt:stage}.json):DEPLOYMENT_BUCKET}  # References S3 bucket for deployment artifacts from environment config
  iam:
    role: CustomRole             # References the IAM role defined in the Resources section
  apiGateway:
    # References existing API Gateway resources from environment config files
    restApiId: ${file(../../../environments/${opt:stage}.json):REST_API_ID}  # Uses existing API Gateway ID
    restApiRootResourceId: ${file(../../../environments/${opt:stage}.json):ROOT_RESOURCE_ID}  # References root resource ID
    restApiResources:
      # Maps API path to resource ID from environment-specific config
      /providers : ${file(./environments/${opt:stage}.json):PROVIDERS_RESOURCE_ID}
  tracing:
    lambda: false                # Disables AWS X-Ray tracing for Lambda functions

functions:
  providers_CRUDPlatform_RDS_S3:  # Defines the Lambda function for Platform CRUD operations

    handler: providers_CRUDPlatform_RDS_S3.lambda_handler  # Points to the function in the Python file that AWS Lambda will invoke
    memorySize: 128  # Allocates 128MB of memory to the Lambda function (minimal for simple operations)
    runtime: python3.12  # Specifies Python 3.12 runtime for this function (can override provider setting)
    logRetentionInDays: 14  # Sets CloudWatch Logs retention period to 14 days
    timeout: 5  # Sets maximum execution time to 5 seconds before timing out
    environment:  # Defines environment variables accessible to the Lambda function
      S3_BUCKET: ${file(../../../environments/${opt:stage}.json):S3_BUCKET}  # Gets S3 bucket name from stage-specific config
      STAGE_NAME: ${file(../../../environments/${opt:stage}.json):STAGE_NAME}  # Gets deployment stage name from config
      CLOUDFRONT_URL: ${file(../../../environments/${opt:stage}.json):CLOUDFRONT_URL}  # Gets CloudFront URL for serving S3 assets
    ephemeralStorageSize: 512  # Allocates 512MB of ephemeral storage for temporary files
    name: providers_CRUDPlatform_RDS_S3  # Sets the function name in AWS
    description: "this is desc"  # Provides a description visible in AWS console
    architecture: x86_64  # Specifies x86_64 architecture (instead of ARM64)
    layers:  # Attaches Lambda layers for shared code and dependencies
      - arn:aws:lambda:us-east-1:${file(../../../environments/${opt:stage}.json):AWS_ACCOUNT_ID}:layer:Base:1  # References Base layer with common utilities

    events:  # Defines events that trigger this Lambda function
      # GET endpoint configuration
      - http:  # Configures API Gateway HTTP endpoint
          path: /providers/platforms/{entityname}  # Sets the API path with entityname parameter
          method: get  # Configures this endpoint for HTTP GET requests
          integration: lambda  # Uses Lambda integration type (vs. Lambda proxy)
          cors:  # Enables Cross-Origin Resource Sharing
            origin: '*'  # Allows requests from any origin
            headers: '*'  # Allows all headers in requests
          private: false  # Makes endpoint publicly accessible
          authorizer:  # Configures API Gateway authorizer for authentication
            type: ${file(../../../environments/${opt:stage}.json):AUTHORIZER_TYPE}  # Gets authorizer type from config
            authorizerId: ${file(../../../environments/${opt:stage}.json):PRIVATE_AUTHORIZER_ID}  # References existing authorizer
          request:  # Configures request handling
            passThrough: WHEN_NO_TEMPLATES  # Passes request through when no template matches
            template:  # Defines mapping template to transform request
              application/json: '{  # Template for JSON content type
                                    "method": "$context.httpMethod",  # Includes HTTP method
                                    "body" : $input.json("$"),  # Passes request body
                                    "entity":"$util.escapeJavaScript($input.params().path.entityname)",  # Extracts entityname from path
                                    "queryParams": {  # Processes query parameters
                                        #foreach($param in $input.params().querystring.keySet())
                                        "$param": "$util.escapeJavaScript($input.params().querystring.get($param))"
                                        #if($foreach.hasNext),#end
                                        #end
                                    },
                                    "headers": {  # Processes request headers
                                        #foreach($param in $input.params().header.keySet())
                                        "$param": "$util.escapeJavaScript($input.params().header.get($param))"
                                        #if($foreach.hasNext),#end
                                        #end
                                    },
                                    "pathParams": {  # Processes path parameters
                                        #foreach($param in $input.params().path.keySet())
                                        "$param": "$util.escapeJavaScript($input.params().path.get($param))"
                                        #if($foreach.hasNext),#end
                                        #end
                                    },
                                    "authorizer_context":{  # Includes authorizer context
                                        "user_id": "$context.authorizer.user_id"
                                    }
                                }'
          response:  # Configures response handling
            statusCodes:  # Maps Lambda responses to HTTP status codes
              200:  # Success response
                pattern: ''  # Default pattern for success (no specific matching)
              400:  # Client error response
                pattern: .*"statusCode"\s*:\s*4\d{2}.*  # Regex pattern to match 4xx errors
                template:  # Template for formatting error responses
                  application/json: >-  # Multi-line template
                    #set($inputRoot = $input.path('$'))  # Sets variable for response root
                    #set($errorMessageObj = $util.parseJson($input.path('$.errorMessage')))  # Parses error message
                    {
                      "error" : "$errorMessageObj.error",  # Extracts error message
                      "is_success": false  # Sets success flag to false
                    }
              500:  # Server error response
                pattern: .*"statusCode"\s*:\s*5\d{2}.*  # Regex pattern to match 5xx errors
                template:  # Template for formatting error responses
                  application/json: >-  # Multi-line template
                    #set($inputRoot = $input.path('$'))  # Sets variable for response root
                    #set($errorMessageObj = $util.parseJson($input.path('$.errorMessage')))  # Parses error message
                    {
                      "error" : "$errorMessageObj.error",  # Extracts error message
                      "is_success": false  # Sets success flag to false
                    }

      # POST endpoint configuration (similar structure to GET but for POST method)
      - http:
          path: /providers/platforms/{entityname}  # Same path as GET endpoint
          method: post  # Configures for HTTP POST requests
          integration: lambda
          cors:
            origin: '*'
            headers: '*'
          private: false
          authorizer:
            type: ${file(../../../environments/${opt:stage}.json):AUTHORIZER_TYPE}
            authorizerId: ${file(../../../environments/${opt:stage}.json):PRIVATE_AUTHORIZER_ID}
          request:
            passThrough: WHEN_NO_TEMPLATES
            template:
              application/json: '{  # Similar template structure as GET
                                    "method": "$context.httpMethod",
                                    "body" : $input.json("$"),
                                    "entity":"$util.escapeJavaScript($input.params().path.entityname)",
                                    "queryParams": {
                                        #foreach($param in $input.params().querystring.keySet())
                                        "$param": "$util.escapeJavaScript($input.params().querystring.get($param))"
                                        #if($foreach.hasNext),#end
                                        #end
                                    },
                                    "headers": {
                                        #foreach($param in $input.params().header.keySet())
                                        "$param": "$util.escapeJavaScript($input.params().header.get($param))"
                                        #if($foreach.hasNext),#end
                                        #end
                                    },
                                    "pathParams": {
                                        #foreach($param in $input.params().path.keySet())
                                        "$param": "$util.escapeJavaScript($input.params().path.get($param))"
                                        #if($foreach.hasNext),#end
                                        #end
                                    },
                                    "authorizer_context":{
                                        "user_id": "$context.authorizer.user_id"
                                    }
                                }'
          response:  # Same response configuration as GET
            statusCodes:
              200:
                pattern: ''
              400:
                pattern: .*"statusCode"\s*:\s*4\d{2}.*
                template:
                  application/json: >-
                    #set($inputRoot = $input.path('$'))
                    #set($errorMessageObj = $util.parseJson($input.path('$.errorMessage')))
                    {
                      "error" : "$errorMessageObj.error",
                      "is_success": false
                    }
              500:
                pattern: .*"statusCode"\s*:\s*5\d{2}.*
                template:
                  application/json: >-
                    #set($inputRoot = $input.path('$'))
                    #set($errorMessageObj = $util.parseJson($input.path('$.errorMessage')))
                    {
                      "error" : "$errorMessageObj.error",
                      "is_success": false
                    }

      # PUT
      - http:
          path: /providers/platforms/{entityname}  # Defines API endpoint path with dynamic entityname parameter
          method: put  # Specifies HTTP PUT method for updating existing resources
          integration: lambda  # Uses Lambda integration type instead of Lambda proxy
          cors:
            origin: '*'  # Allows cross-origin requests from any domain
            headers: '*'  # Permits all headers in cross-origin requests
          private: false  # Makes endpoint publicly accessible (not requiring API key)
          authorizer:  # Configures authorization for this endpoint
            type: ${file(../../../environments/${opt:stage}.json):AUTHORIZER_TYPE}  # Gets authorizer type from environment config
            authorizerId: ${file(../../../environments/${opt:stage}.json):PRIVATE_AUTHORIZER_ID}  # References existing authorizer ID 
          request:  # Configures request handling
            passThrough: WHEN_NO_TEMPLATES  # Passes request through when no template matches
            template:  # Defines mapping template to transform API Gateway request to Lambda event
              application/json: '{
                                    "method": "$context.httpMethod",  # Includes HTTP method in Lambda event
                                    "body" : $input.json("$"),  # Passes request body to Lambda
                                    "entity":"$util.escapeJavaScript($input.params().path.entityname)",  # Extracts entityname from path
                                    "queryParams": {  # Processes and includes query parameters
                                        #foreach($param in $input.params().querystring.keySet())
                                        "$param": "$util.escapeJavaScript($input.params().querystring.get($param))"
                                        #if($foreach.hasNext),#end
                                        #end
                                    },
                                    "headers": {  # Processes and includes request headers
                                        #foreach($param in $input.params().header.keySet())
                                        "$param": "$util.escapeJavaScript($input.params().header.get($param))"
                                        #if($foreach.hasNext),#end
                                        #end
                                    },
                                    "pathParams": {  # Processes and includes path parameters
                                        #foreach($param in $input.params().path.keySet())
                                        "$param": "$util.escapeJavaScript($input.params().path.get($param))"
                                        #if($foreach.hasNext),#end
                                        #end
                                    },
                                    "authorizer_context":{  # Includes authorizer context for access control
                                        "user_id": "$context.authorizer.user_id"
                                    }
                                }'
          response:  # Configures response handling
            statusCodes:  # Maps Lambda responses to HTTP status codes
              200:  # Success response configuration
                pattern: ''  # Default pattern for success (matches any response not matching other patterns)
              400:  # Client error response configuration
                pattern: .*"statusCode"\s*:\s*4\d{2}.*  # Regex pattern to match 4xx errors in Lambda response
                template:  # Template for formatting error responses
                  application/json: >-  # Multi-line template for JSON response
                    #set($inputRoot = $input.path('$'))  # Sets variable for response root
                    #set($errorMessageObj = $util.parseJson($input.path('$.errorMessage')))  # Parses error message from Lambda
                    {
                      "error" : "$errorMessageObj.error",  # Extracts error message
                      "is_success": false  # Sets success flag to false
                    }
              500:  # Server error response configuration
                pattern: .*"statusCode"\s*:\s*5\d{2}.*  # Regex pattern to match 5xx errors in Lambda response
                template:  # Template for formatting error responses
                  application/json: >-  # Multi-line template for JSON response
                    #set($inputRoot = $input.path('$'))  # Sets variable for response root
                    #set($errorMessageObj = $util.parseJson($input.path('$.errorMessage')))  # Parses error message from Lambda
                    {
                      "error" : "$errorMessageObj.error",  # Extracts error message
                      "is_success": false  # Sets success flag to false
                    }

      # DELETE
      - http:
          path: /providers/platforms/{entityname}  # Defines API endpoint path with dynamic entityname parameter
          method: delete  # Specifies HTTP DELETE method for removing resources
          integration: lambda  # Uses Lambda integration type instead of Lambda proxy
          cors:
            origin: '*'  # Allows cross-origin requests from any domain
            headers: '*'  # Permits all headers in cross-origin requests
          private: false  # Makes endpoint publicly accessible (not requiring API key)
          authorizer:  # Configures authorization for this endpoint
            type: ${file(../../../environments/${opt:stage}.json):AUTHORIZER_TYPE}  # Gets authorizer type from environment config
            authorizerId: ${file(../../../environments/${opt:stage}.json):PRIVATE_AUTHORIZER_ID}  # References existing authorizer ID
          request:  # Configures request handling
            passThrough: WHEN_NO_TEMPLATES  # Passes request through when no template matches
            template:  # Defines mapping template to transform API Gateway request to Lambda event
              application/json: '{
                                    "method": "$context.httpMethod",  # Includes HTTP method in Lambda event
                                    "entity":"$util.escapeJavaScript($input.params().path.entityname)",  # Extracts entityname from path
                                    "body" : $input.json("$"),  # Passes request body to Lambda
                                    "queryParams": {  # Processes and includes query parameters
                                        #foreach($param in $input.params().querystring.keySet())
                                        "$param": "$util.escapeJavaScript($input.params().querystring.get($param))"
                                        #if($foreach.hasNext),#end
                                        #end
                                    },
                                    "headers": {  # Processes and includes request headers
                                        #foreach($param in $input.params().header.keySet())
                                        "$param": "$util.escapeJavaScript($input.params().header.get($param))"
                                        #if($foreach.hasNext),#end
                                        #end
                                    },
                                    "pathParams": {  # Processes and includes path parameters
                                        #foreach($param in $input.params().path.keySet())
                                        "$param": "$util.escapeJavaScript($input.params().path.get($param))"
                                        #if($foreach.hasNext),#end
                                        #end
                                    },
                                    "authorizer_context":{  # Includes authorizer context for access control
                                        "user_id": "$context.authorizer.user_id"
                                    }
                                }'

          response:  # Configures response handling
            statusCodes:  # Maps Lambda responses to HTTP status codes
              200:  # Success response configuration
                pattern: ''  # Default pattern for success (matches any response not matching other patterns)
              400:  # Client error response configuration
                pattern: .*"statusCode"\s*:\s*4\d{2}.*  # Regex pattern to match 4xx errors in Lambda response
                template:  # Template for formatting error responses
                  application/json: >-  # Multi-line template for JSON response
                    #set($inputRoot = $input.path('$'))  # Sets variable for response root
                    #set($errorMessageObj = $util.parseJson($input.path('$.errorMessage')))  # Parses error message from Lambda
                    {
                      "error" : "$errorMessageObj.error",  # Extracts error message
                      "is_success": false  # Sets success flag to false
                    }
              500:  # Server error response configuration
                pattern: .*"statusCode"\s*:\s*5\d{2}.*  # Regex pattern to match 5xx errors in Lambda response
                template:  # Template for formatting error responses
                  application/json: >-  # Multi-line template for JSON response
                    #set($inputRoot = $input.path('$'))  # Sets variable for response root
                    #set($errorMessageObj = $util.parseJson($input.path('$.errorMessage')))  # Parses error message from Lambda
                    {
                      "error" : "$errorMessageObj.error",  # Extracts error message
                      "is_success": false  # Sets success flag to false
                    }

resources:
  Resources:
    CustomRole:  # Defines a custom IAM role for Lambda execution
      Type: AWS::IAM::Role  # CloudFormation resource type for IAM roles
      Properties:
        ManagedPolicyArns:  # Attaches AWS managed policies to provide predefined permissions
          - 'arn:aws:iam::aws:policy/service-role/AWSLambdaRole'  # Basic Lambda execution permissions
          - 'arn:aws:iam::aws:policy/AWSLambdaExecute'  # Permissions for Lambda to access CloudWatch Logs and S3
          - 'arn:aws:iam::aws:policy/AmazonS3FullAccess'  # Full access to S3 for file operations (platform images)
          - 'arn:aws:iam::aws:policy/AmazonDynamoDBReadOnlyAccess'  # Read-only access to DynamoDB tables
          - 'arn:aws:iam::aws:policy/SecretsManagerReadWrite'  # Access to AWS Secrets Manager for credentials
        AssumeRolePolicyDocument:  # Defines which services can assume this role (trust relationship)
          Version: '2012-10-17'  # Policy document version (standard for IAM policies)
          Statement:
            - Effect: Allow  # Allows Lambda service to assume this role
              Principal:
                Service:
                  - 'lambda.amazonaws.com'  # Lambda service principal
              Action:
                - 'sts:AssumeRole'  # Security Token Service action to assume the role
            - Effect: Allow  # Allows API Gateway service to assume this role
              Principal:
                Service:
                  - 'apigateway.amazonaws.com'  # API Gateway service principal
              Action:
                - 'sts:AssumeRole'  # Security Token Service action to assume the role
              Condition:  # Restricts role assumption to specific API Gateway resources
                ArnLike:  # Condition operator that uses ARN pattern matching
                  AWS:SourceArn: arn:aws:execute-api:us-east-1:${file(../../../environments/${opt:stage}.json):AWS_ACCOUNT_ID}:${file(../../../environments/${opt:stage}.json):REST_API_ID}/*/*/*  # Restricts to specific API Gateway ARN pattern with wildcards for stage/resource/method
