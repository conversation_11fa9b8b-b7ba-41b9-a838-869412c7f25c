"""
Platform CRUD Operations Lambda Handler

This module implements a serverless AWS Lambda function that provides CRUD operations
for platform-related entities in the RealtyFeed system. It handles two main entities:
- platform: Represents integration platforms with properties like name, credentials, and UI access
- platform_credential: Stores authentication details for connecting to external platforms

Key components:
1. Entity Configuration (entity_lookup_data):
   - Defines database tables, validation rules, and acceptable fields for each entity
   - Configures filtering options and permissions

2. HTTP Method Handlers:
   - Uses Strategy pattern with abstract HttpMethodHandler base class
   - Concrete implementations for GET, POST, PUT, DELETE operations
   - Each handler manages specific database operations and S3 file handling

3. Lambda Handler:
   - Processes API Gateway requests with proper error handling
   - Validates input data against entity-specific rules
   - Delegates to appropriate HTTP method handler
   - Manages database connections and transaction commits
   - Returns standardized response format

The module integrates with:
- RDS PostgreSQL for data persistence
- AWS S3 for file storage (platform images)
- Custom validation logic for data integrity
"""

from abc import ABC, abstractmethod  # Imports abstract base class functionality for creating the strategy pattern
from datetime import datetime  # Used for timestamping record creation and modification
import json  # For parsing and serializing JSON data in API requests/responses
import logging  # Provides logging capabilities for debugging and monitoring
import psycopg2  # PostgreSQL database adapter for Python

from realtyfeed.datasets import sample_response  # Imports template for standardized API responses
from realtyfeed.exceptions import DatabaseException, DataInvalidException  # Custom exceptions for error handling
from realtyfeed.orm import (  # Object-Relational Mapping utilities for database operations
    RDSHandler,  # Main handler for RDS database operations
    DeleteRecordException,  # Exception for delete operation failures
    UpdateRecordException,  # Exception for update operation failures
    GetRecordException,  # Exception for retrieval operation failures
    CreateRecordException,  # Exception for creation operation failures
)
from validations import (  # Input validation utilities
    validate_data,  # Main validation orchestrator function
    platform_post_put_input_data_validator,  # Validator for platform entity
    platform_credential_post_put_input_data_validator,  # Validator for platform_credential entity
)
from utils import *  # Imports all utilities including S3 operations and constants


logger = logging.getLogger()  # Creates a logger instance for this module


# The lambda looks up entities data from this variable.

#### add new entity(table) here with new fields
entity_lookup_data = {  # Configuration dictionary that defines entity metadata and validation rules
    PLATFORM: {  # Configuration for platform entity
        TABLE: "platforms",  # Database table name for platform records
        INPUT_VALIDATOR_FUNCTION: platform_post_put_input_data_validator,  # Function to validate platform input data
        ACCEPTABLE_INPUT_FIELDS: {  # Set of field names that are allowed in requests
            "name",  # Platform name
            "image",  # Path to platform logo/image
            "dashboard_access_url",  # URL for accessing platform dashboard
            "username",  # Username for platform authentication
            "password",  # Password for platform authentication
            "extra_data",  # JSON field for additional platform-specific data
        },
        LIST_FILTER_ACCEPTABLE_FIELDS_KEY: ["id", "name", "username"],  # Fields that can be used in filter queries
        ACCEPTABLE_SELECTED_FIELDS: "*",  # Allowed fields in SELECT queries (all fields)
        PARENT_ENTITY_FOR_PERMISSIONS: MLS,  # Parent entity for permission inheritance
    },
    PLATFORM_CREDENTIAL: {  # Configuration for platform_credential entity
        TABLE: "platform_credentials",  # Database table name for credential records
        INPUT_VALIDATOR_FUNCTION: platform_credential_post_put_input_data_validator,  # Function to validate credential input data
        ACCEPTABLE_INPUT_FIELDS: {  # Set of field names that are allowed in requests
            "name",  # Credential name
            "platform_id",  # Foreign key to associated platform
            "feed_type_connection_type_id",  # Type of feed connection
            "token_url",  # URL for token generation
            "request_url",  # URL for API requests
            "client_id",  # Client ID for authentication
            "client_password",  # Client password/secret for authentication
            "access_token",  # OAuth access token
            "scope",  # OAuth scope
            "generate_token",  # Flag to auto-generate tokens
            "options",  # JSON field for additional credential-specific options
        },
        LIST_FILTER_ACCEPTABLE_FIELDS_KEY: [  # Fields that can be used in filter queries
            "id",
            "name",
            "platform_id",
            "feed_type_connection_type_id",
            "client_id",
            "scope",
        ],
        ACCEPTABLE_SELECTED_FIELDS: "*",  # Allowed fields in SELECT queries (all fields)
        PARENT_ENTITY_FOR_PERMISSIONS: MLS,  # Parent entity for permission inheritance
    },
}
#### should becarfull with relations

class HttpMethodHandler(ABC):  # Abstract base class for HTTP method handlers (Strategy pattern)

    def __init__(self, rds_handler: RDSHandler):  # Constructor that accepts database handler
        self.rds_handler = rds_handler  # Stores database handler for use in concrete implementations

    @abstractmethod  # Decorator that marks method as abstract (must be implemented by subclasses)
    def handle(self, response: dict, entity: str, data: dict):  # Abstract method signature for handling HTTP requests
        return  # Placeholder return statement


class GETHttpMethodHandler(HttpMethodHandler):  # Concrete implementation for handling GET requests
    def handle(self, response: dict, entity: str, data: dict):  # Implements abstract handle method
        id = data.get("id")  # Extracts record ID from request data if present
        list_filter = data.get("list_filter")  # Extracts filter parameters if present

        if id:  # If ID is provided, retrieve a single record
            result = self.rds_handler.get_record(id=id)  # Fetch record by ID

        elif list_filter:  # If filter parameters are provided, retrieve filtered records

            try:
                # NOTE: If the fields were used in filter is not in acceptable fields list,
                # the generate_sql_condition_by_filter() method raise DataInvalidException,
                # else it generate a sql statement condition and its placeholders.
                # The result of the generate_sql_condition_by_filter() method is a dict like this:
                # {
                #   "sql_where":"sql condition with the place-holders name
                #   "plceholders":{} # A dict containes the place-holders name as key and its value.
                # }
                acceptable_list_filter_fields = entity_lookup_data[entity][  # Gets list of fields that can be used in filters
                    LIST_FILTER_ACCEPTABLE_FIELDS_KEY
                ]
                filters = list_filter.get("filters", None)  # Extracts filter conditions
                sql_condition = self.rds_handler.generate_sql_condition_by_filter(  # Generates SQL WHERE clause from filter conditions
                    conditions=filters,
                    acceptable_fields=acceptable_list_filter_fields,
                )

            except DataInvalidException as e:  # Handles validation errors in filter fields
                # This Exception was handled in the validate_data() function
                # and this error code and the message are the same as that code and message.
                logger.exception(f"ERROR | DataInvalidException e={e}")  # Logs the exception
                response["message_code"] = f"{ERROR_CODE_PREFIX}-03"  # Sets error code in response
                response["error"] = [  # Adds detailed error information
                    {
                        "code": f"{ERROR_CODE_PREFIX}-03091",  # Specific error code for filter field validation
                        "message": f"Acceptable fields for using in filters are {entity_lookup_data[entity][LIST_FILTER_ACCEPTABLE_FIELDS_KEY]} fields.",  # Error message listing allowed filter fields
                    }
                ]
                return response  # Returns error response without querying database

            # Get record from RDS
            result_count = self.rds_handler.get_record(  # Counts total matching records for pagination
                where=sql_condition["sql_where"],  # SQL WHERE clause
                placeholders_values_object=sql_condition["plceholders"],  # Parameter values for prepared statement
                select_fields=f"{entity_lookup_data[entity][TABLE]}.id",  # Only selects ID field for counting
            )
            result = self.rds_handler.get_record(  # Retrieves actual records with pagination
                where=sql_condition["sql_where"],  # SQL WHERE clause
                placeholders_values_object=sql_condition["plceholders"],  # Parameter values for prepared statement
                order_by=list_filter.get("order_by", ""),  # Optional sorting
                limit=list_filter.get("limit", ""),  # Optional result limit
                offset=list_filter.get("offset", ""),  # Optional pagination offset
            )
            response["result_count"] = len(result_count)  # Adds total count to response for pagination
        return response, result  # Returns response object and query results


class POSTHttpMethodHandler(HttpMethodHandler):
    def handle(self, response, entity, data):
        now = datetime.now().isoformat()  # Creates ISO-formatted timestamp for audit trail
        data["creation_date"] = now  # Adds creation timestamp to record metadata
        data["modification_date"] = now  # Initially sets modification date same as creation date

        # Create record in RDS
        result = self.rds_handler.create_record(
            # convert_json used for adding json fields in record.
            # convert_json is necessary for 'extra_data' field in Platform entity
            # and 'options' field in 'platform_credential' entity.
            convetr_json_columns=True,  # Automatically converts Python dict to JSON string for JSON fields
            data_object=data,  # Passes the complete data object to be inserted into database
        )

        # If entity is platform and hase image, do some S3 operations.
        if entity == PLATFORM and data.get("image"):  # Special handling for platform images

            # Creating new path
            path = data.get("image")  # Gets temporary image path from request data
            new_path = f"{PLATFORM}s/{result['id']}/images"  # Constructs permanent path using entity ID

            # Moving image from temp space to permanently space in S3
            new_fiel_url = change_s3(new_path=new_path, file_path=path)  # Moves file and returns new URL

            # Updating created platform record 'image' field in RDS
            result = self.rds_handler.update_record(  # Updates the record with permanent image URL
                data_object={"id": result["id"], "image": new_fiel_url},  # Only updates image field
                returning="*",  # Returns complete updated record
            )
        return response, result  # Returns response object and created record


class PUTHttpMethodHandler(HttpMethodHandler):
    def handle(self, response, entity, data):
        data["modification_date"] = datetime.now().isoformat()  # Updates modification timestamp for audit trail

        # If entity is platform and hase image, do some S3 operations.
        if entity == PLATFORM and data.get("image"):  # Special handling for platform images
            # 1- Removing the previus image from S3
            platform_record = self.rds_handler.get_record(id=data["id"])  # Retrieves existing record to get old image
            delete_file_from_s3(platform_record.get("image"))  # Deletes old image from S3

            # 2- Adding new image/document to S3
            id = data.get("id")  # Gets record ID for constructing path
            path = data.get("image")  # Gets temporary image path from request data
            new_path = f"{PLATFORM}s/{data['id']}/images"  # Constructs permanent path using entity ID
            data["image"] = change_s3(new_path=new_path, file_path=path)  # Moves file and updates image URL in data

        # Updating record in RDS
        result = self.rds_handler.update_record(  # Updates the database record
            id=data.get("id"),  # Specifies which record to update
            data_object=data,  # Passes complete data object with all fields to update
            convetr_json_columns=True,  # Automatically converts Python dict to JSON string for JSON fields
            returning="*",  # Returns complete updated record
        )
        return response, result  # Returns response object and updated record


class DELETEHttpMethodHandler(HttpMethodHandler):
    def handle(self, response, entity, data):
        # Deleting record from RDS
        result = self.rds_handler.delete_record(id=data.get("id"), returning="*")  # Deletes record and returns deleted data

        # If entity is platform and hase image, delete its image from S3.
        if entity == PLATFORM and result.get("image"):  # Special handling for platform images
            delete_file_from_s3(result.get("image"))  # Cleans up associated image file from S3
        return response, result  # Returns response object and deleted record


def lambda_handler(event, context):
    try:
        logger.info(f"event: {event}")  # Logs the incoming event for debugging and monitoring
        event_body = event.get("body", event)  # Extracts request body or uses entire event if body not present
        query_params = event.get("queryParams", {})  # Gets query parameters from event or empty dict if none
        http_method = event.get("method", "GET")  # Determines HTTP method, defaulting to GET if not specified
        entity = event["entity"]  # Extracts the entity type (platform or platform_credential) from the event

        data = (
            json.loads(query_params.get("data"))  # For GET requests, data comes from query parameters as JSON string
            if http_method == "GET"
            else event_body.get("data")  # For other methods, data comes directly from request body
        )
        response = sample_response.copy()  # Creates a new copy of the response template to avoid modifying the original

        # validating input data and making lower case choice fields.
        errors = validate_data(entity_lookup_data, entity, data, http_method)  # Validates input data against entity rules
        if errors:  # If validation errors were found
            logger.info(f"validation errors= {str(errors)}")  # Logs validation errors
            response["message_code"] = f"{ERROR_CODE_PREFIX}-03"  # Sets standard error code for validation failures
            response["error"] = errors  # Includes detailed validation errors in response
            return response  # Returns early with validation error response

        # Getting RDS handler
        try:
            rds_handler = RDSHandler(
                table_name=entity_lookup_data[entity][TABLE],  # Uses entity-specific table name from configuration
                is_cursor_dict=True  # Configures cursor to return results as dictionaries instead of tuples
            )
        except DatabaseException as e:  # Handles database connection failures
            logger.exception(f"ERROR | Database connection error. e={e}")  # Logs database connection error
            response["message_code"] = f"{ERROR_CODE_PREFIX}-04"  # Sets error code for database connection issues
            raise Exception("Database connection error.")  # Re-raises as generic exception to be caught by outer try/except

        # Doing Operations
        result = None  # Initializes result variable
        if http_method == "GET":  # Strategy pattern - selects handler based on HTTP method
            handler = GETHttpMethodHandler(rds_handler)  # Creates handler for GET operations
        elif http_method == "POST":
            handler = POSTHttpMethodHandler(rds_handler)  # Creates handler for POST operations
        elif http_method == "PUT":
            handler = PUTHttpMethodHandler(rds_handler)  # Creates handler for PUT operations
        elif http_method == "DELETE":
            handler = DELETEHttpMethodHandler(rds_handler)  # Creates handler for DELETE operations
        response, result = handler.handle(response, entity, data)  # Delegates operation to appropriate handler
        rds_handler.commit_changes()  # Commits any pending database transactions
        response["is_success"] = True  # Marks response as successful
        response["result"] = result  # Includes operation result in response

    except psycopg2.errors.NoDataFound as e:  # Special case for record not found
        # NOTE: The record not found is not a real error, And the response 'is_success' should not be False in this case.
        logger.exception(f"ERROR | Record Not Found | e={e}")  # Logs record not found as error for tracking
        response["message_code"] = NOT_FOUND_RECORD_ERROR_CODE  # Sets specific error code for record not found
        response["error"] = (
            f"ERROR | Record Not Found | the record that you want not found."  # Sets user-friendly error message
        )
        response["is_success"] = True  # Still marks as success since this is an expected condition
        response["result"] = []  # Returns empty result array

    except GetRecordException as e:  # Handles errors during record retrieval
        logger.exception(f"ERROR={e}")  # Logs the exception details
        response["message_code"] = f"{ERROR_CODE_PREFIX}-06"  # Sets error code for get operation failures
        response["error"] = (
            f"ERROR | {e.__class__.__name__} | An error occurred while getting record/records."  # Sets descriptive error message
        )

    except CreateRecordException as e:  # Handles errors during record creation
        logger.exception(f"ERROR={e}")  # Logs the exception details
        response["message_code"] = f"{ERROR_CODE_PREFIX}-07"  # Sets error code for create operation failures
        response["error"] = (
            f"ERROR | {e.__class__.__name__} | An error occurred while creating record."  # Sets descriptive error message
        )

    except UpdateRecordException as e:  # Handles errors during record updates
        logger.exception(f"ERROR={e}")  # Logs the exception details
        response["message_code"] = f"{ERROR_CODE_PREFIX}-08"  # Sets error code for update operation failures
        response["error"] = (
            f"ERROR | {e.__class__.__name__} | An error occurred while updating record."  # Sets descriptive error message
        )

    except DeleteRecordException as e:  # Handles errors during record deletion
        logger.exception(f"ERROR={e}")  # Logs the exception details
        response["message_code"] = f"{ERROR_CODE_PREFIX}-09"  # Sets error code for delete operation failures
        response["error"] = (
            f"ERROR | {e.__class__.__name__} | An error occurred while deleting record."  # Sets descriptive error message
        )

    except S3ActionException as e:  # Handles errors during S3 file operations
        logger.exception(f"ERROR={e}")  # Logs the exception details
        response["message_code"] = f"{ERROR_CODE_PREFIX}-10"  # Sets error code for S3 operation failures
        response["error"] = (
            f"ERROR | {e.__class__.__name__} | An error occurred while doing AWS-S3 action."  # Sets descriptive error message
        )

    except Exception as e:  # Catches any other unexpected exceptions
        logger.exception(f"ERROR={e}")  # Logs the exception details
        if response["message_code"] == "":  # Only sets message code if not already set
            response["message_code"] = f"{ERROR_CODE_PREFIX}-11"  # Sets generic error code for unexpected errors
        response["error"] = (
            f"ERROR | {e.__class__.__name__} | An unexpected error occurred."  # Sets generic error message
        )

    if ("rds_handler" in locals() or "rds_handler" in globals()) and rds_handler.conn:  # Checks if database connection exists
        rds_handler.close_connection()  # Ensures database connection is properly closed even after exceptions

    return response  # Returns the final response object
